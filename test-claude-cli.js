"use strict";
/**
 * Claude CLI 测试文件
 * 用于测试 Claude CLI 封装类的基本功能
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var claude_cli_1 = require("./src/lib/claude-cli");
// 测试配置
var TEST_CONFIG = {
    proxy: "http://127.0.0.1:7897",
    idleTimeoutMs: 3000, // 3秒超时，测试时可以短一些
};
// 测试提示词
var TEST_PROMPTS = [
    "你好，请简单介绍一下你自己。",
    "请用一句话解释什么是TypeScript。",
    "写一个简单的JavaScript函数，用于计算两个数的和。"
];
/**
 * 基础连接测试
 */
function testBasicConnection() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            console.log('\n🧪 开始基础连接测试...');
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var testCompleted = false;
                    var claude = new claude_cli_1.default(__assign(__assign({}, TEST_CONFIG), { onReady: function () {
                            console.log('✅ Claude CLI 连接成功！');
                            // 连接成功后立即销毁，完成测试
                            setTimeout(function () {
                                claude.destroy();
                                if (!testCompleted) {
                                    testCompleted = true;
                                    console.log('✅ 基础连接测试通过');
                                    resolve();
                                }
                            }, 1000);
                        }, onData: function (data) {
                            console.log('📥 接收到数据:', data.slice(0, 100) + (data.length > 100 ? '...' : ''));
                        } }));
                    // 设置测试超时
                    setTimeout(function () {
                        if (!testCompleted) {
                            testCompleted = true;
                            claude.destroy();
                            reject(new Error('基础连接测试超时'));
                        }
                    }, 15000); // 15秒超时
                })];
        });
    });
}
/**
 * 简单对话测试
 */
function testSimpleConversation() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            console.log('\n🧪 开始简单对话测试...');
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var testCompleted = false;
                    var responseReceived = false;
                    var claude = new claude_cli_1.default(__assign(__assign({}, TEST_CONFIG), { onReady: function () {
                            console.log('✅ Claude CLI 准备就绪，发送测试消息...');
                            claude.send(TEST_PROMPTS[0]);
                        }, onData: function (data) {
                            console.log('📥 Claude 响应:', data.slice(0, 200) + (data.length > 200 ? '...' : ''));
                            // 如果收到了实质性的响应（不只是提示符），标记为成功
                            if (data.length > 10 && !responseReceived) {
                                responseReceived = true;
                                setTimeout(function () {
                                    claude.destroy();
                                    if (!testCompleted) {
                                        testCompleted = true;
                                        console.log('✅ 简单对话测试通过');
                                        resolve();
                                    }
                                }, 2000);
                            }
                        } }));
                    // 设置测试超时
                    setTimeout(function () {
                        if (!testCompleted) {
                            testCompleted = true;
                            claude.destroy();
                            if (responseReceived) {
                                console.log('✅ 简单对话测试通过（超时但已收到响应）');
                                resolve();
                            }
                            else {
                                reject(new Error('简单对话测试超时，未收到响应'));
                            }
                        }
                    }, 30000); // 30秒超时
                })];
        });
    });
}
/**
 * 代码生成测试
 */
function testCodeGeneration() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            console.log('\n🧪 开始代码生成测试...');
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var testCompleted = false;
                    var fullResponse = '';
                    var claude = new claude_cli_1.default(__assign(__assign({}, TEST_CONFIG), { onReady: function () {
                            console.log('✅ Claude CLI 准备就绪，发送代码生成请求...');
                            claude.send(TEST_PROMPTS[2]);
                        }, onData: function (data) {
                            fullResponse += data;
                            console.log('📥 接收代码片段:', data.slice(0, 100) + (data.length > 100 ? '...' : ''));
                            // 检查是否包含代码块标记
                            if (fullResponse.includes('```') || fullResponse.includes('function')) {
                                setTimeout(function () {
                                    claude.destroy();
                                    if (!testCompleted) {
                                        testCompleted = true;
                                        console.log('✅ 代码生成测试通过');
                                        console.log('📄 生成的代码片段:', fullResponse.slice(0, 300) + '...');
                                        resolve();
                                    }
                                }, 3000);
                            }
                        } }));
                    // 设置测试超时
                    setTimeout(function () {
                        if (!testCompleted) {
                            testCompleted = true;
                            claude.destroy();
                            if (fullResponse.length > 50) {
                                console.log('✅ 代码生成测试通过（超时但已收到响应）');
                                resolve();
                            }
                            else {
                                reject(new Error('代码生成测试超时，未收到有效响应'));
                            }
                        }
                    }, 45000); // 45秒超时
                })];
        });
    });
}
/**
 * 重置功能测试
 */
function testResetFunction() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            console.log('\n🧪 开始重置功能测试...');
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var testCompleted = false;
                    var readyCount = 0;
                    var claude = new claude_cli_1.default(__assign(__assign({}, TEST_CONFIG), { onReady: function () {
                            readyCount++;
                            console.log("\u2705 Claude CLI \u7B2C ".concat(readyCount, " \u6B21\u51C6\u5907\u5C31\u7EEA"));
                            if (readyCount === 1) {
                                // 第一次准备就绪后，测试重置功能
                                setTimeout(function () {
                                    console.log('🔄 测试重置功能...');
                                    claude.reset();
                                }, 1000);
                            }
                            else if (readyCount === 2) {
                                // 重置后再次准备就绪，测试通过
                                setTimeout(function () {
                                    claude.destroy();
                                    if (!testCompleted) {
                                        testCompleted = true;
                                        console.log('✅ 重置功能测试通过');
                                        resolve();
                                    }
                                }, 1000);
                            }
                        }, onData: function (data) {
                            console.log('📥 重置测试数据:', data.slice(0, 50) + '...');
                        } }));
                    // 设置测试超时
                    setTimeout(function () {
                        if (!testCompleted) {
                            testCompleted = true;
                            claude.destroy();
                            reject(new Error('重置功能测试超时'));
                        }
                    }, 20000); // 20秒超时
                })];
        });
    });
}
/**
 * 运行所有测试
 */
function runAllTests() {
    return __awaiter(this, void 0, void 0, function () {
        var tests, passedTests, failedTests, _i, tests_1, test, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log('🚀 开始 Claude CLI 功能测试');
                    console.log('📋 测试配置:', TEST_CONFIG);
                    tests = [
                        { name: '基础连接测试', fn: testBasicConnection },
                        { name: '简单对话测试', fn: testSimpleConversation },
                        { name: '代码生成测试', fn: testCodeGeneration },
                        { name: '重置功能测试', fn: testResetFunction },
                    ];
                    passedTests = 0;
                    failedTests = 0;
                    _i = 0, tests_1 = tests;
                    _a.label = 1;
                case 1:
                    if (!(_i < tests_1.length)) return [3 /*break*/, 8];
                    test = tests_1[_i];
                    _a.label = 2;
                case 2:
                    _a.trys.push([2, 4, , 5]);
                    console.log("\n".concat('='.repeat(50)));
                    console.log("\uD83E\uDDEA \u6267\u884C\u6D4B\u8BD5: ".concat(test.name));
                    console.log("".concat('='.repeat(50)));
                    return [4 /*yield*/, test.fn()];
                case 3:
                    _a.sent();
                    passedTests++;
                    console.log("\u2705 ".concat(test.name, " - \u901A\u8FC7"));
                    return [3 /*break*/, 5];
                case 4:
                    error_1 = _a.sent();
                    failedTests++;
                    console.error("\u274C ".concat(test.name, " - \u5931\u8D25:"), error_1 instanceof Error ? error_1.message : error_1);
                    return [3 /*break*/, 5];
                case 5:
                    if (!(test !== tests[tests.length - 1])) return [3 /*break*/, 7];
                    console.log('\n⏳ 等待 3 秒后继续下一个测试...');
                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 3000); })];
                case 6:
                    _a.sent();
                    _a.label = 7;
                case 7:
                    _i++;
                    return [3 /*break*/, 1];
                case 8:
                    // 输出测试结果
                    console.log("\n".concat('='.repeat(60)));
                    console.log('📊 测试结果汇总');
                    console.log("".concat('='.repeat(60)));
                    console.log("\u2705 \u901A\u8FC7: ".concat(passedTests, " \u4E2A\u6D4B\u8BD5"));
                    console.log("\u274C \u5931\u8D25: ".concat(failedTests, " \u4E2A\u6D4B\u8BD5"));
                    console.log("\uD83D\uDCC8 \u6210\u529F\u7387: ".concat(((passedTests / tests.length) * 100).toFixed(1), "%"));
                    if (failedTests === 0) {
                        console.log('\n🎉 所有测试都通过了！Claude CLI 工作正常。');
                    }
                    else {
                        console.log('\n⚠️ 部分测试失败，请检查 Claude CLI 配置和网络连接。');
                    }
                    return [2 /*return*/];
            }
        });
    });
}
// 主函数
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var error_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4 /*yield*/, runAllTests()];
                case 1:
                    _a.sent();
                    return [3 /*break*/, 3];
                case 2:
                    error_2 = _a.sent();
                    console.error('❌ 测试执行失败:', error_2);
                    process.exit(1);
                    return [3 /*break*/, 3];
                case 3: return [2 /*return*/];
            }
        });
    });
}
// 处理未捕获的异常
process.on('unhandledRejection', function (reason, promise) {
    console.error('❌ 未处理的 Promise 拒绝:', reason);
    process.exit(1);
});
process.on('uncaughtException', function (error) {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});
// 运行测试
if (require.main === module) {
    main();
}
// export { runAllTests, testBasicConnection, testSimpleConversation, testCodeGeneration, testResetFunction };
testBasicConnection();
