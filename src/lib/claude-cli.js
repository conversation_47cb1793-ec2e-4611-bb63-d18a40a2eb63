"use strict";
/**
 * Claude CLI 封装类 (TypeScript版本)
 * 用于管理和控制 Claude CLI 进程
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClaudeCLI = void 0;
var pty = require("node-pty");
var ClaudeCLI = /** @class */ (function () {
    function ClaudeCLI(options) {
        if (options === void 0) { options = {}; }
        this.proxy = options.proxy || "http://127.0.0.1:7897";
        this.idleTimeoutMs = options.idleTimeoutMs || 2000; // 连续 2 秒无输出
        this.ready = false;
        this.idleTimer = null;
        this.ptyProcess = null;
        this.onReadyCallback = options.onReady || null;
        this.onDataCallback = options.onData || null;
        // 初始化 Claude CLI 实例
        this._initializeCLI();
    }
    ClaudeCLI.prototype._initializeCLI = function () {
        var _this = this;
        var env = __assign(__assign({}, process.env), { http_proxy: this.proxy, https_proxy: this.proxy });
        this.ptyProcess = pty.spawn("claude", ["agent"], {
            name: "xterm-color",
            cols: 100,
            rows: 30,
            cwd: process.cwd(),
            env: env,
        });
        this.ready = false;
        this._resetIdleTimer();
        // 输出监听
        this.ptyProcess.onData(function (data) {
            if (_this.onDataCallback) {
                _this.onDataCallback(data);
            }
            else {
                process.stdout.write(data);
            }
            _this._resetIdleTimer();
        });
        // 监听进程退出
        this.ptyProcess.onExit(function (exitCode, signal) {
            console.log("\n\u26A0\uFE0F Claude CLI \u8FDB\u7A0B\u9000\u51FA\uFF0C\u9000\u51FA\u7801: ".concat(exitCode, ", \u4FE1\u53F7: ").concat(signal));
            _this._clearIdleTimer();
        });
    };
    ClaudeCLI.prototype._resetIdleTimer = function () {
        var _this = this;
        this._clearIdleTimer();
        this.idleTimer = setTimeout(function () {
            if (!_this.ready) {
                _this.ready = true;
                console.log("\n✅ 检测到 2s 无输出，Claude Agent 已 ready");
                if (_this.onReadyCallback) {
                    _this.onReadyCallback();
                }
            }
        }, this.idleTimeoutMs);
    };
    ClaudeCLI.prototype._clearIdleTimer = function () {
        if (this.idleTimer) {
            clearTimeout(this.idleTimer);
            this.idleTimer = null;
        }
    };
    ClaudeCLI.prototype.send = function (prompt) {
        var _this = this;
        if (!this.ptyProcess) {
            throw new Error("Claude CLI 实例未初始化");
        }
        // 清空当前输入行（Ctrl+U）
        this.ptyProcess.write("\u0015");
        // 输入文本
        this.ptyProcess.write(prompt);
        // 模拟延迟后按下回车（更稳妥）
        setTimeout(function () {
            if (_this.ptyProcess) {
                _this.ptyProcess.write("\r");
            }
        }, 300);
    };
    ClaudeCLI.prototype.reset = function () {
        console.log("🔄 重置 Claude CLI 实例...");
        // 清理当前实例
        this._clearIdleTimer();
        if (this.ptyProcess) {
            this.ptyProcess.kill();
            this.ptyProcess = null;
        }
        // 重新初始化
        this._initializeCLI();
        console.log("✅ Claude CLI 实例已重置");
    };
    ClaudeCLI.prototype.isReady = function () {
        return this.ready;
    };
    ClaudeCLI.prototype.destroy = function () {
        this._clearIdleTimer();
        if (this.ptyProcess) {
            this.ptyProcess.kill();
            this.ptyProcess = null;
        }
    };
    return ClaudeCLI;
}());
exports.ClaudeCLI = ClaudeCLI;
exports.default = ClaudeCLI;
