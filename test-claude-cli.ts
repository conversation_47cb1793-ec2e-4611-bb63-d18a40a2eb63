/**
 * Claude CLI 测试文件
 * 用于测试 Claude CLI 封装类的基本功能
 */

import Claude<PERSON><PERSON> from './src/lib/claude-cli';

// 测试配置
const TEST_CONFIG = {
  proxy: "http://127.0.0.1:7897",
  idleTimeoutMs: 3000, // 3秒超时，测试时可以短一些
};

// 测试提示词
const TEST_PROMPTS = [
  "你好，请简单介绍一下你自己。",
  "请用一句话解释什么是TypeScript。",
  "写一个简单的JavaScript函数，用于计算两个数的和。"
];

/**
 * 基础连接测试
 */
async function testBasicConnection(): Promise<void> {
  console.log('\n🧪 开始基础连接测试...');
  
  return new Promise((resolve, reject) => {
    let testCompleted = false;
    
    const claude = new ClaudeCLI({
      ...TEST_CONFIG,
      onReady: () => {
        console.log('✅ Claude CLI 连接成功！');
        
        // 连接成功后立即销毁，完成测试
        setTimeout(() => {
          claude.destroy();
          if (!testCompleted) {
            testCompleted = true;
            console.log('✅ 基础连接测试通过');
            resolve();
          }
        }, 1000);
      },
      onData: (data: string) => {
        console.log('📥 接收到数据:', data.slice(0, 100) + (data.length > 100 ? '...' : ''));
      }
    });

    // 设置测试超时
    setTimeout(() => {
      if (!testCompleted) {
        testCompleted = true;
        claude.destroy();
        reject(new Error('基础连接测试超时'));
      }
    }, 15000); // 15秒超时
  });
}

/**
 * 简单对话测试
 */
async function testSimpleConversation(): Promise<void> {
  console.log('\n🧪 开始简单对话测试...');
  
  return new Promise((resolve, reject) => {
    let testCompleted = false;
    let responseReceived = false;
    
    const claude = new ClaudeCLI({
      ...TEST_CONFIG,
      onReady: () => {
        console.log('✅ Claude CLI 准备就绪，发送测试消息...');
        claude.send(TEST_PROMPTS[0]);
      },
      onData: (data: string) => {
        console.log('📥 Claude 响应:', data.slice(0, 200) + (data.length > 200 ? '...' : ''));
        
        // 如果收到了实质性的响应（不只是提示符），标记为成功
        if (data.length > 10 && !responseReceived) {
          responseReceived = true;
          
          setTimeout(() => {
            claude.destroy();
            if (!testCompleted) {
              testCompleted = true;
              console.log('✅ 简单对话测试通过');
              resolve();
            }
          }, 2000);
        }
      }
    });

    // 设置测试超时
    setTimeout(() => {
      if (!testCompleted) {
        testCompleted = true;
        claude.destroy();
        if (responseReceived) {
          console.log('✅ 简单对话测试通过（超时但已收到响应）');
          resolve();
        } else {
          reject(new Error('简单对话测试超时，未收到响应'));
        }
      }
    }, 30000); // 30秒超时
  });
}

/**
 * 代码生成测试
 */
async function testCodeGeneration(): Promise<void> {
  console.log('\n🧪 开始代码生成测试...');
  
  return new Promise((resolve, reject) => {
    let testCompleted = false;
    let fullResponse = '';
    
    const claude = new ClaudeCLI({
      ...TEST_CONFIG,
      onReady: () => {
        console.log('✅ Claude CLI 准备就绪，发送代码生成请求...');
        claude.send(TEST_PROMPTS[2]);
      },
      onData: (data: string) => {
        fullResponse += data;
        console.log('📥 接收代码片段:', data.slice(0, 100) + (data.length > 100 ? '...' : ''));
        
        // 检查是否包含代码块标记
        if (fullResponse.includes('```') || fullResponse.includes('function')) {
          setTimeout(() => {
            claude.destroy();
            if (!testCompleted) {
              testCompleted = true;
              console.log('✅ 代码生成测试通过');
              console.log('📄 生成的代码片段:', fullResponse.slice(0, 300) + '...');
              resolve();
            }
          }, 3000);
        }
      }
    });

    // 设置测试超时
    setTimeout(() => {
      if (!testCompleted) {
        testCompleted = true;
        claude.destroy();
        if (fullResponse.length > 50) {
          console.log('✅ 代码生成测试通过（超时但已收到响应）');
          resolve();
        } else {
          reject(new Error('代码生成测试超时，未收到有效响应'));
        }
      }
    }, 45000); // 45秒超时
  });
}

/**
 * 重置功能测试
 */
async function testResetFunction(): Promise<void> {
  console.log('\n🧪 开始重置功能测试...');
  
  return new Promise((resolve, reject) => {
    let testCompleted = false;
    let readyCount = 0;
    
    const claude = new ClaudeCLI({
      ...TEST_CONFIG,
      onReady: () => {
        readyCount++;
        console.log(`✅ Claude CLI 第 ${readyCount} 次准备就绪`);
        
        if (readyCount === 1) {
          // 第一次准备就绪后，测试重置功能
          setTimeout(() => {
            console.log('🔄 测试重置功能...');
            claude.reset();
          }, 1000);
        } else if (readyCount === 2) {
          // 重置后再次准备就绪，测试通过
          setTimeout(() => {
            claude.destroy();
            if (!testCompleted) {
              testCompleted = true;
              console.log('✅ 重置功能测试通过');
              resolve();
            }
          }, 1000);
        }
      },
      onData: (data: string) => {
        console.log('📥 重置测试数据:', data.slice(0, 50) + '...');
      }
    });

    // 设置测试超时
    setTimeout(() => {
      if (!testCompleted) {
        testCompleted = true;
        claude.destroy();
        reject(new Error('重置功能测试超时'));
      }
    }, 20000); // 20秒超时
  });
}

/**
 * 运行所有测试
 */
async function runAllTests(): Promise<void> {
  console.log('🚀 开始 Claude CLI 功能测试');
  console.log('📋 测试配置:', TEST_CONFIG);
  
  const tests = [
    { name: '基础连接测试', fn: testBasicConnection },
    { name: '简单对话测试', fn: testSimpleConversation },
    { name: '代码生成测试', fn: testCodeGeneration },
    { name: '重置功能测试', fn: testResetFunction },
  ];

  let passedTests = 0;
  let failedTests = 0;

  for (const test of tests) {
    try {
      console.log(`\n${'='.repeat(50)}`);
      console.log(`🧪 执行测试: ${test.name}`);
      console.log(`${'='.repeat(50)}`);
      
      await test.fn();
      passedTests++;
      console.log(`✅ ${test.name} - 通过`);
      
    } catch (error) {
      failedTests++;
      console.error(`❌ ${test.name} - 失败:`, error instanceof Error ? error.message : error);
    }
    
    // 测试间隔
    if (test !== tests[tests.length - 1]) {
      console.log('\n⏳ 等待 3 秒后继续下一个测试...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // 输出测试结果
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 测试结果汇总');
  console.log(`${'='.repeat(60)}`);
  console.log(`✅ 通过: ${passedTests} 个测试`);
  console.log(`❌ 失败: ${failedTests} 个测试`);
  console.log(`📈 成功率: ${((passedTests / tests.length) * 100).toFixed(1)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 所有测试都通过了！Claude CLI 工作正常。');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查 Claude CLI 配置和网络连接。');
  }
}

// 主函数
async function main(): Promise<void> {
  try {
    await runAllTests();
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 运行测试
if (require.main === module) {
  main();
}

// export { runAllTests, testBasicConnection, testSimpleConversation, testCodeGeneration, testResetFunction };
testBasicConnection()